--shouweixiangyang.lua...b(function()

shouweixiangyang = {
	new = function()
		local hw = {}
		setmetatable(hw, { __index = helloworld })
		return hw
	end,
	open_ask = function()
		open_trigger("shouweixiangyang_ask_1")
		open_trigger("shouweixiangyang_ask_2")
		open_trigger("shouweixiangyang_ask_3")
		open_trigger("shouweixiangyang_ask_4")
		open_trigger("shouweixiangyang_ask_5")
		open_trigger("shouweixiangyang_ask_6")
		open_trigger("shouweixiangyang_ask_16")
	end,
	close_ask = function()
		close_trigger("shouweixiangyang_ask_1")
		close_trigger("shouweixiangyang_ask_2")
		close_trigger("shouweixiangyang_ask_3")
		close_trigger("shouweixiangyang_ask_4")
		close_trigger("shouweixiangyang_ask_5")
		close_trigger("shouweixiangyang_ask_6")
		close_trigger("shouweixiangyang_ask_16")
	end,
	open_job = function()

	end,
	close_job = function()

	end,
	open_finish = function()

	end,
	close_finish = function()

	end,
}
add_trigger("shouweixiangyang_1", "^[ > ]*你向吕文德打听有关『守卫襄阳』的消息。", function(params)
	open_trigger("shouweixiangyang_ask_1")
	open_trigger("shouweixiangyang_ask_2")
	open_trigger("shouweixiangyang_ask_3")
	open_trigger("shouweixiangyang_ask_4")
	open_trigger("shouweixiangyang_ask_5")
	open_trigger("shouweixiangyang_ask_6")
	del_timer("input")
	close_trigger("shouweixiangyang_1")
	close_trigger("shouweixiangyang_16")
	shouweixiangyang:open_ask()
end)
---------------
--您先歇口气再说话吧。
add_trigger("shouweixiangyang_16", "^[ > ]*您先歇口气再说话吧。", function(params)
	set_wield_weapon("swxy")
	check_busy(function()
		exec("ask lv wende about 守卫襄阳")
	end)
end)
--问任务----------
add_trigger("shouweixiangyang_ask_1",
	"^[ > ]*(吕文德对你说道：我这里现在没有什么任务，你等会再来吧。|吕文德对你说道：您上次任务辛苦了，还是先休息一下再说吧。|吕文德对你说道：镜像已经开启太多，欢迎下次参与。)", function(params) --busy
	var["fangqi_job"] = "swxy"
	if var["exp"] and var["exp"] > 1000000 and var["time_mark_swxy"] == nil then
		var["fangqi_job"] = nil
		do_log("swxy_busy")
		var["time_mark_swxy"] = os.time() - 300 --总等待 10 分钟，如果问了一下那么等待5分钟
	end
	wait(5, function()
		check_busy(function()
			exec("changejob")
			close_ask_swxy()
		end)
	end)
end)

add_trigger("shouweixiangyang_ask_2", "^[ > ]*吕文德对你说道：我看你獐头鼠目，不象个好人，如何能放心把军机大事托付给你。", function(params)
	--close_ask_swxy()
	var["idle"] = 0
	check_busy(function()
		exec("do_quest shen_up") --提高shen
		close_ask_swxy()
	end)
end)

add_trigger("shouweixiangyang_ask_3", "^[ > ]*吕文德对你说道：不知道这次能不能守下来。", function(params) --swxy
	--close_ask_swxy()
	set_start_time("守卫襄阳")
	b(function()
		close_ask_swxy()
		do_log("swxy_start")
		shouweixiangyang:swxy()
	end)
end)
close_trigger("shouweixiangyang_1")
--close_ask_swxy()



add_alias("set_job_swxy", function(params)
	var["job_room"] = nil
	var["job_zone"] = nil
	var["killer_name"] = nil
	var["killer_id"] = nil
	var["pfm_id"] = nil
	var["job_start"] = nil --没看到蒙面人
	var["room_list"] = nil
	var["search_list"] = nil
	var["flag_job"] = "swxy" --cisha
	close_fight()       --关闭战斗触发
	var["fight"] = 0    --不在战斗状态
	var["idle"] = 0     --没发呆

	send("alias bei_skills " .. var["skills_bei_smy"])

	add_alias("kill_job_npc", function(params)
		var["pfm_id"] = "menggu wushi"
		local first_pfm = get_first_pfm(var["first_pfm_swxy"])
		exec("kill @pfm_id;kill @pfm_id 2;" .. first_pfm)
	end)


	add_alias("job_escape", function(params) --逃跑
		local swxy_escape = var["swxy_escape"] or 1
		if swxy_escape then
			var["not_wrong_way"] = nil
			npc_in_maze_found = function()
				return nil
			end --迷宫发现npc
			npc_in_maze_action = function()
				return nil --迷宫发现npc的行为
			end
			echo("\n" .. C.c .. "<Lua>:" .. os.date("%m/%d %H:%M:%S") .. C.x .. "【守卫襄阳】：战斗中出现各种未知异常，撤退！")
			open_trigger("fight_44")
			send("set wimpy 100")
			exec("set wimpycmd halt\\get @myweapon\\ed\\lead @pfm_id\\alias action 战斗失败，立即撤退...")
			exes("halt;s;look;lead @pfm_id;alias action 战斗失败，立即撤退...", 2)
			exec("halt;s;halt;s")
			var["time_mark_swxy"] = os.time() - 420 --失败了，提前点时间继续接守卫襄阳
			close_fight()
			var["no_need_heal_qi"] = nil --可以疗伤
			var["do_stop"] = 0
			--close_swxy()
		else
			echo("\n" .. C.c .. "<Lua>:" .. os.date("%m/%d %H:%M:%S") .. C.x .. "【守卫襄阳】：强力党继续战斗！")
		end
	end)

	add_alias("job_ask", function(params) --ask
		g(363, function()
			set_wield_weapon("swxy")
			open_trigger("shouweixiangyang_1")
			send("cond")
			send("askk lv wende about 守卫襄阳")
		end)
	end)

	add_alias("job_fail", function(params)
		var["time_mark_swxy"] = os.time() - 420 --失败后，2分钟后就开始重新守卫襄阳
		--		exec("do_quit quit")
		close_fight()
		var["no_need_heal_qi"] = nil --可以疗伤
		var["do_stop"] = 0
		exec("s;changejob")
	end)

	add_alias("job_win", function(params)
		var["time_mark_swxy"] = os.time()
		var["no_need_heal_qi"] = nil --可以疗伤
		close_fight()
		var["do_stop"] = 0
		exec('changejob')
	end)

	add_alias("after_faint", function(params) -- 【after_faint】：设置杀晕倒以后干嘛
		exec("job_fail")
	end)

	add_alias("after_faint2", function(params) -- 【after_faint2】：设置杀晕倒以后干嘛
		exec("job_fail")
	end)

	add_alias("do_job_swxy", function(params)
		var["killer_name"] = "蒙古士兵"
		var["killer_id"] = "menggu wushi"
		var["pfm_id"] = "menggu wushi"
		set_dazuo("swxy")
		exec("go_dazuo")
		echo("\n" .. C.W .. "守卫襄阳任务：第 " .. C.Y .. var["swxy_count"] .. C.W .. " 组。")
	end)
end)


function shouweixiangyang:swxy() --守卫襄阳
	local swxy_fangqi_skills = var["swxy_fangqi_skills"] or "玄阴1剑法|灵蛇1杖法|天山1杖法"
	--"玄阴剑法|灵蛇杖法|天山杖法"	
	if var["swxy_hs_use_zj"] == 1 then
		swxy_hs_use_zj = true
	end
	exec('set_job_swxy')
	var["flag_job"] = "swxy"  --cisha
	if var["swxy_pfm"] == nil then --如果未设置默认的守卫襄阳pfm，那么把var["swxy_pfm"]=var["pfm1"]
		var["swxy_pfm"] = var["smy_pfm"]
	end
	--远处的山路传来一阵轻啸，隐约听得有人施展轻功飞驰而来。
	add_trigger("shouweixiangyang_2", "^[ > ]*蒙古士兵翻越了城墙，撕开了守军一个又一个的防守点，你带着增援的兵卒，从驰道上冲上了城墙。", function(params)
		var["swxy_count"] = var["swxy_count"] + 1
		--echo("\n"..C.W.."守卫襄阳任务：第 "..C.Y..var["swxy_count"]..C.W.." 组。")
		echo("\n" .. C.c .. "<Lua>:" .. os.date("%m/%d %H:%M:%S") .. C.x ..
		"【守卫襄阳】：第 " .. C.Y .. var["swxy_count"] .. C.W .. " 组。")
		var["idle"] = 0
		open_fight()
		--exec("kill_job_npc")

		send("look menggu wushi 1")
		send("look menggu wushi 2")
		--set_fight("swxy")
	end)
	add_trigger("shouweixiangyang_3", "^[ > ]*只听得噗通一声，眼见得你四脚朝天，跌倒在地上。",
		function(params)
			var["wushi1_party"] = nil
			var["wushi2_party"] = nil
			var["wushi1_skill"] = nil
			var["wushi2_skill"] = nil
			var["wushi1_name"] = nil
			var["wushi2_name"] = nil
			var["wushi1_id"] = nil
			var["wushi2_id"] = nil
			var["wushi1_faint"] = nil
			var["wushi2_faint"] = nil
			var["zj_target_1"] = 0 --战斗开始之前，标示总决未曾命中目标一
			var["zj_target_2"] = 0 --战斗开始之前，标示总决未曾命中目标二
			if var["swxy_hs_use_zj"] == 1 then
				swxy_hs_use_zj = true
			end
			var["idle"] = 0
			wait(40, function()
				Print("停止所有行为等待敌人！")
				send("halt")			
				unset_timer("timer")
				close_heal()
				close_dazuo() 
				check_busy(function()
					exec("yun_powerup none")
				end)
			end)
		end)

	--
	--	看起来过甲想杀死你！
	--	看起来郝端想杀死你！
	--吴马崔只觉得处处受制，武功中厉害之处完全无法发挥出来！
	--凌苗灵机一动，身随剑走，果然你再也无法随意出招。

	--独孤九剑总决式，命中和没命中的触发
	add_trigger("shouweixiangyang_3_1", "^看起来(.*)想杀死你！", function(params)
		if var["wushi1_name"] == nil then
			var["wushi1_name"] = params[1]
			--var["wushi1_id"]=get_id(params[1])
			--echo("\n"..C.W..var["wushi1_name"]..var["wushi1_id"])
		else
			var["wushi2_name"] = params[1]
			--var["wushi2_id"]=get_id(params[1])
			--echo("\n"..C.W..var["wushi2_name"]..var["wushi2_id"])
		end
		if var["swxy_hs_use_zj"] and var["swxy_hs_use_zj"] == 1 then
			add_trigger("shouweixiangyang_zj_1", "^(.*)只觉得处处受制，武功中厉害之处完全无法发挥出来！", function(params)
				--var["zj_target_hit"]=trim(params[1])

				if trim(params[1]) == var["wushi1_name"] then
					var["zj_target_1"] = 1
					var["fight"] = 1
					var["pfm_id"] = var["wushi2_id"]
					send("alias pfm " .. expand(var["swxy_pfm_dgjj"]))
					send("alias pfm_backup " .. expand(var["swxy_pfm_dgjj"]))
					send("set wimpy 100")
					send("set wimpycmd hp " .. var["char_id"] .. "\\pfm")
					send("pfm")
					echo("\n" ..
					C.c ..
					"<Lua>:" ..
					os.date("%m/%d %H:%M:%S") ..
					C.x .. "【守卫襄阳】： " .. C.g .. var["wushi1_name"] ..
					C.W .. " 已经被总决打中，转换目标为 " .. C.g .. var["wushi2_name"] .. " ")
				end
				if trim(params[1]) == var["wushi2_name"] then
					var["zj_target_2"] = 1
					var["fight"] = 1
					var["pfm_id"] = var["wushi1_id"]
					send("alias pfm " .. expand(var["swxy_pfm_dgjj"]))
					send("alias pfm_backup " .. expand(var["swxy_pfm_dgjj"]))
					send("set wimpy 100")
					send("set wimpycmd hp " .. var["char_id"] .. "\\pfm")
					send("pfm")
					echo("\n" ..
					C.c ..
					"<Lua>:" ..
					os.date("%m/%d %H:%M:%S") ..
					C.x .. "【守卫襄阳】： " .. C.g .. var["wushi2_name"] ..
					C.W .. " 已经被总决打中，转换目标为 " .. C.g .. var["wushi1_name"] .. " ")
				end
				if var["zj_target_1"] == 1 and var["zj_target_2"] == 1 then
					--总决全部命中，换用杀伤性pfm
					swxy_hs_use_zj = false --可以不用总决pfm了
					send("alias pfm " .. expand(var["swxy_pfm"]))
					send("alias pfm_backup " .. expand(var["swxy_pfm"]))
					send("set wimpy 100")
					send("set wimpycmd hp " .. var["char_id"] .. "\\pfm")
					send("pfm")
					echo("\n" .. C.c .. "<Lua>:" .. os.date("%m/%d %H:%M:%S") .. C.x .. "【守卫襄阳】：  目标全部被总决了，转换为普通杀伤性技能！ ")
				end
			end)
			add_trigger("shouweixiangyang_zj_2", "^(.*)灵机一动，身随剑走，果然你再也无法随意出招。", function(params)
				--var["zj_target_miss"]=trim(params[1])
				if trim(params[1]) == var["wushi1_name"] then
					var["zj_target_1"] = 0
					echo("\n" ..
					C.c .. "<Lua>:" ..
					os.date("%m/%d %H:%M:%S") .. C.x .. "【守卫襄阳】： " .. C.g .. var["wushi1_name"] .. C.W .. " 总决未命中！ ")
				end
				if trim(params[1]) == var["wushi2_name"] then
					var["zj_target_2"] = 0
					echo("\n" ..
					C.c .. "<Lua>:" ..
					os.date("%m/%d %H:%M:%S") .. C.x .. "【守卫襄阳】： " .. C.g .. var["wushi2_name"] .. C.W .. " 总决未命中！ ")
				end
			end)
		end
	end)

	--	千夫长 郝端(Hao duan)
	add_trigger("shouweixiangyang_3_2", "^千夫长\\s*(\\S+)\\((.*)\\)", function(params)
		if var["wushi1_name"] ~= nil and var["wushi1_name"] == params[1] then
			var["wushi1_id"] = string.lower(params[2])
			--echo("\n"..C.W..var["wushi1_name"].." "..var["wushi1_id"])
		end
		if var["wushi2_name"] ~= nil and var["wushi2_name"] == params[1] then
			var["wushi2_id"] = string.lower(params[2])
			--echo("\n"..C.W..var["wushi2_name"].." "..var["wushi2_id"])
		end
	end)




	--	这是西夏一品堂从江湖上重金招聘的一名武林高手。
	--	此人看上去师承逍遥派，擅长使用如意刀法伤敌！
	--	line1=此人看上去师承逍遥派，擅长使用如意刀法伤敌！
	--	line2=这是西夏一品堂从江湖上重金招聘的一名武林高手。
	--	line3=千夫长 郝端(Hao duan)

	add_trigger("shouweixiangyang_4", "^[ > ]*此人看上去师承(.*)，擅长使用(.*)伤敌！", function(params)
		--echo("\n"..C.W.."line3="..line[3])
		if var["wushi1_party"] == nil and string.find(line[3], var["wushi1_name"]) then
			var["wushi1_party"] = params[1]
			var["wushi1_skill"] = params[2]
			if var["wushi1_name"] and var["wushi1_id"] and var["wushi1_party"] and var["wushi1_skill"] then
				echo("\n" ..
				C.c ..
				"<Lua>:" ..
				os.date("%m/%d %H:%M:%S") ..
				C.x ..
				"【守卫襄阳】：【" ..
				C.g ..
				var["wushi1_party"] ..
				C.W .. "】的【" .. C.g .. var["wushi1_name"] .. C.W .. "】使用【" .. C.g .. var["wushi1_skill"] .. C.W .. "】")
				if string.find(swxy_fangqi_skills, var["wushi1_skill"]) then --加入放弃技能列表
					check_busy(function()
						exec("job_escape")
					end)
					echo("\n" ..
					C.c ..
					"<Lua>:" .. os.date("%m/%d %H:%M:%S") ..
					C.x .. "【守卫襄阳】：遇到不可力敌的【" .. C.g .. var["wushi1_skill"] .. C.W .. "】，撤退！")
				end
			end
		elseif var["wushi2_party"] == nil and string.find(line[3], var["wushi2_name"]) then
			var["wushi2_party"] = params[1]
			var["wushi2_skill"] = params[2]
			if var["wushi2_name"] and var["wushi2_id"] and var["wushi2_party"] and var["wushi2_skill"] then
				echo("\n" ..
				C.c ..
				"<Lua>:" ..
				os.date("%m/%d %H:%M:%S") ..
				C.x ..
				"【守卫襄阳】：【" ..
				C.g ..
				var["wushi2_party"] ..
				C.W .. "】的【" .. C.g .. var["wushi2_name"] .. C.W .. "】使用【" .. C.g .. var["wushi2_skill"] .. C.W .. "】")
				if string.find(swxy_fangqi_skills, var["wushi2_skill"]) then --加入放弃技能列表
					check_busy(function()
						exec("job_escape")
					end)
					echo("\n" ..
					C.c ..
					"<Lua>:" .. os.date("%m/%d %H:%M:%S") ..
					C.x .. "【守卫襄阳】：遇到不可力敌的【" .. C.g .. var["wushi2_skill"] .. C.W .. "】，撤退！")
				end
			end
		end
		if var["wushi1_skill"] ~= nil and var["wushi2_skill"] ~= nil then --如果两个武士的技能都判断好之后，开始根据玩家自设的技能优先级，进行pfm对象的优先排序
			local wushi1_skill = var["wushi1_skill"]
			local wushi2_skill = var["wushi2_skill"]
			local wushi1_id = var["wushi1_id"]
			local wushi2_id = var["wushi2_id"]
			local skills_pri_list = var["swxy_skills_list"] or "玄阴剑法|灵蛇杖法|天山杖法|天羽奇剑|圣火令法|独孤九剑|玉女素心剑|七弦无形剑|回风拂柳剑|如意刀法"
			local wushi1_pri = string.find(skills_pri_list, wushi1_skill) or 0 --比较优先级技能列表，如果搜索到技能，则获取对应位置，否则为0
			local wushi2_pri = string.find(skills_pri_list, wushi2_skill) or 0 --比较优先级技能列表，如果搜索到技能，则获取对应位置，否则为0

			if wushi1_pri >= wushi2_pri then                       --技能优先级 武士1>=武士2,击杀顺序不用变
				echo("\n" ..
				C.c ..
				"<Lua>:" ..
				os.date("%m/%d %H:%M:%S") ..
				C.x .. "【守卫襄阳】：发现【" .. C.g .. var["wushi1_skill"] .. C.W .. "】优先对付【" .. C.g ..
				var["wushi1_name"] .. C.W .. "】")
				var["fight"] = 1
				var["pfm_id"] = var["wushi1_id"]
				var["pfm1_id"] = var["wushi2_id"]
				if swxy_hs_use_zj then
					echo("\n" ..
					C.c .. "<Lua>:" .. os.date("%m/%d %H:%M:%S") ..
					C.x .. "【守卫襄阳】：总决对付【" .. C.g .. var["wushi1_name"] .. C.W .. "】")
					local sp_pfm_exist = false
					for i = 1, 10 do       --10 个技能和对应的Pfm，应该够用了吧？
						if var["swxy_sp_skill" .. i] == nil then --如果未设置特殊pfm，那么强制设置为空字符串 防止比较过程中出错
							var["swxy_sp_skill" .. i] = ""
						end
						if var["swxy_sp_skill" .. i] == var["wushi1_skill"] then
							--echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."【守卫襄阳】：发现特殊技能列表【"..var["swxy_sp_skill"..i].."】与【"..var["wushi1_skill"].."】匹配！")					
							sp_pfm_exist = true          --特殊技能有匹配，那么不能用默认值
							send("alias pfm " .. expand(var["swxy_sp_pfm" .. i])) --特殊技能对应特殊pfm如果有匹配的话，那么pfm设置为对应的特殊pfm
							send("alias pfm_backup " .. expand(var["swxy_sp_pfm" .. i]))
						end
					end
					if not sp_pfm_exist and var[var["swxy_pfm_dgjj"]] ~= "" then --没有匹配的特殊Pfm并且设置了使用独孤九剑，那么使用独孤九剑总决式
						send("alias pfm " .. expand(var["swxy_pfm_dgjj"]))
						send("alias pfm_backup " .. expand(var["swxy_pfm_dgjj"]))
					end
					send("set wimpy 100")
					send("set wimpycmd hp " .. var["char_id"] .. "\\pfm")
					send("pfm")
				else
					echo("\n" ..
					C.c .. "<Lua>:" ..
					os.date("%m/%d %H:%M:%S") .. C.x .. "【守卫襄阳】：杀伤技能对付【" .. C.g .. var["wushi1_name"] .. C.W .. "】")
					local sp_pfm_exist = false
					for i = 1, 10 do       --10 个技能和对应的Pfm，应该够用了吧？
						if var["swxy_sp_skill" .. i] == nil then --如果未设置特殊pfm，那么强制设置为空字符串 防止比较过程中出错
							var["swxy_sp_skill" .. i] = ""
						end
						if var["swxy_sp_skill" .. i] == var["wushi1_skill"] then
							--echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."【守卫襄阳】：发现特殊技能列表【"..var["swxy_sp_skill"..i].."】与【"..var["wushi1_skill"].."】匹配！")					
							sp_pfm_exist = true          --特殊技能有匹配，那么不能用默认值
							send("alias pfm " .. expand(var["swxy_sp_pfm" .. i])) --特殊技能对应特殊pfm如果有匹配的话，那么pfm设置为对应的特殊pfm
							send("alias pfm_backup " .. expand(var["swxy_sp_pfm" .. i]))
						end
					end
					if not sp_pfm_exist then --没有匹配的特殊Pfm，那么设置为默认的var["swxy_pfm"]
						send("alias pfm " .. expand(var["swxy_pfm"]))
						send("alias pfm_backup " .. expand(var["swxy_pfm"]))
					end
					send("set wimpy 100")
					send("set wimpycmd hp " .. var["char_id"] .. "\\pfm")
					send("pfm")
				end
			end
			if wushi1_pri < wushi2_pri then --技能优先级 武士2> 武士1,优先击杀武士2
				echo("\n" ..
				C.c ..
				"<Lua>:" ..
				os.date("%m/%d %H:%M:%S") ..
				C.x .. "【守卫襄阳】：发现【" .. C.g .. var["wushi2_skill"] .. C.W .. "】优先对付【" .. C.g ..
				var["wushi2_name"] .. C.W .. "】")
				var["fight"] = 1
				var["pfm_id"] = var["wushi2_id"]
				var["pfm1_id"] = var["wushi1_id"]
				if swxy_hs_use_zj then
					--加入swxy专用pfm判断
					--var["swxy_sp_skill1"]="玄阴剑法"
					--var["swxy_sp_pfm1"]="bei none;bei xxx;jifa parry xxx;jifa xxx xxx;jiali max;yun xxx;perform xxx.xxx @pfm_id"
					--例如：对付玄阴剑专用pfm 丐帮的perform pi
					echo("\n" ..
					C.c .. "<Lua>:" .. os.date("%m/%d %H:%M:%S") ..
					C.x .. "【守卫襄阳】：总决对付【" .. C.g .. var["wushi2_name"] .. C.W .. "】")
					local sp_pfm_exist = false
					for i = 1, 10 do       --10 个技能和对应的Pfm，应该够用了吧？
						if var["swxy_sp_skill" .. i] == nil then --如果未设置特殊pfm，那么强制设置为空字符串 防止比较过程中出错
							var["swxy_sp_skill" .. i] = ""
						end
						--echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."【守卫襄阳】：武士2的技能【"..var["wushi2_skill"].."】")
						if var["swxy_sp_skill" .. i] == var["wushi2_skill"] then
							--echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."【守卫襄阳】：发现特殊技能列表【"..var["swxy_sp_skill"..i].."】与【"..var["wushi2_skill"].."】匹配！")	
							sp_pfm_exist = true          --特殊技能有匹配，那么不能用默认值
							send("alias pfm " .. expand(var["swxy_sp_pfm" .. i])) --特殊技能对应特殊pfm如果有匹配的话，那么pfm设置为对应的特殊pfm
							send("alias pfm_backup " .. expand(var["swxy_sp_pfm" .. i]))
						end
					end
					if not sp_pfm_exist and var[var["swxy_pfm_dgjj"]] ~= "" then --没有匹配的特殊Pfm，那么设置为默认的var["swxy_pfm"]
						send("alias pfm " .. expand(var["swxy_pfm_dgjj"]))
						send("alias pfm_backup " .. expand(var["swxy_pfm_dgjj"]))
					end
					send("set wimpy 100")
					send("set wimpycmd hp " .. var["char_id"] .. "\\pfm")
					send("pfm")
				else
					--加入swxy专用pfm判断
					--var["swxy_sp_skill1"]="玄阴剑法"
					--var["swxy_sp_pfm1"]="bei none;bei xxx;jifa parry xxx;jifa xxx xxx;jiali max;yun xxx;perform xxx.xxx @pfm_id"
					--例如：对付玄阴剑专用pfm 丐帮的perform pi
					echo("\n" ..
					C.c .. "<Lua>:" ..
					os.date("%m/%d %H:%M:%S") .. C.x .. "【守卫襄阳】：杀伤技能对付【" .. C.g .. var["wushi2_name"] .. C.W .. "】")
					local sp_pfm_exist = false
					for i = 1, 10 do       --10 个技能和对应的Pfm，应该够用了吧？
						if var["swxy_sp_skill" .. i] == nil then --如果未设置特殊pfm，那么强制设置为空字符串 防止比较过程中出错
							var["swxy_sp_skill" .. i] = ""
						end
						--echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."【守卫襄阳】：武士2的技能【"..var["wushi2_skill"].."】")
						if var["swxy_sp_skill" .. i] == var["wushi2_skill"] then
							--echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."【守卫襄阳】：发现特殊技能列表【"..var["swxy_sp_skill"..i].."】与【"..var["wushi2_skill"].."】匹配！")	
							sp_pfm_exist = true          --特殊技能有匹配，那么不能用默认值
							send("alias pfm " .. expand(var["swxy_sp_pfm" .. i])) --特殊技能对应特殊pfm如果有匹配的话，那么pfm设置为对应的特殊pfm
							send("alias pfm_backup " .. expand(var["swxy_sp_pfm" .. i]))
						end
					end
					if not sp_pfm_exist then --没有匹配的特殊Pfm，那么设置为默认的var["swxy_pfm"]
						send("alias pfm " .. expand(var["swxy_pfm"]))
						send("alias pfm_backup " .. expand(var["swxy_pfm"]))
					end
					send("set wimpy 100")
					send("set wimpycmd hp " .. var["char_id"] .. "\\pfm")
					send("pfm")
				end
			end
		end
	end)

	add_trigger("shouweixiangyang_5_1", "^[ > ]*(\\S+)「啪」的一声倒在地上，挣扎着抽动了几下就死了。", function(params)
		var["swxy_kill_count"] = var["swxy_kill_count"] + 1
		--echo("\n"..C.W.."守卫襄阳任务：第 "..C.Y..var["swxy_count"]..C.W.." 组。")

		--exec("kill_job_npc")

		if var["wushi1_name"] and var["wushi1_name"] == params[1] then --武士1死了
			--改变pfm对象为武士2
			var["wushi1_faint"] = "武士1无威胁"
			if var["wushi2_id"] and var["wushi2_id"] ~= "" then
				var["fight"] = 1
				var["pfm_id"] = var["wushi2_id"]
				--send("alias pfm "..expand(var["pfm1"]))
				--send("alias pfm_backup "..expand(var["pfm1"]))
				--************************************************************************************************
				local sp_pfm_exist = false
				for i = 1, 10 do
					if var["swxy_sp_skill" .. i] == nil then
						var["swxy_sp_skill" .. i] = ""
					end
					if var["swxy_sp_skill" .. i] == var["wushi2_skill"] then
						--echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."【守卫襄阳】：发现特殊技能列表【"..var["swxy_sp_skill"..i].."】与【"..var["wushi2_skill"].."】匹配！")	
						sp_pfm_exist = true
						send("alias pfm " .. expand(var["swxy_sp_pfm" .. i]))
						send("alias pfm_backup " .. expand(var["swxy_sp_pfm" .. i]))
					end
				end
				if not sp_pfm_exist then
					send("alias pfm " .. expand(var["swxy_pfm"]))
					send("alias pfm_backup " .. expand(var["swxy_pfm"]))
				end
				--************************************************************************************************
				send("set wimpy 100")
				send("set wimpycmd hp " .. var["char_id"] .. "\\pfm")
				wait(0.5, function() --等待0.2s以后再fight
					exec("fight " .. var["wushi2_id"])
				end)
			end
		end
		if var["wushi2_name"] and var["wushi2_name"] == params[1] then --武士2死了
			var["wushi2_faint"] = "武士2无威胁"
			--改变pfm对象为武士1
			if var["wushi1_id"] and var["wushi1_id"] ~= "" then
				var["fight"] = 1
				var["pfm_id"] = var["wushi1_id"]
				--send("alias pfm "..expand(var["pfm1"]))
				--send("alias pfm_backup "..expand(var["pfm1"]))
				--************************************************************************************************
				local sp_pfm_exist = false
				for i = 1, 10 do
					if var["swxy_sp_skill" .. i] == nil then
						var["swxy_sp_skill" .. i] = ""
					end
					if var["swxy_sp_skill" .. i] == var["wushi1_skill"] then
						--echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."【守卫襄阳】：发现特殊技能列表【"..var["swxy_sp_skill"..i].."】与【"..var["wushi1_skill"].."】匹配！")	
						sp_pfm_exist = true
						send("alias pfm " .. expand(var["swxy_sp_pfm" .. i]))
						send("alias pfm_backup " .. expand(var["swxy_sp_pfm" .. i]))
					end
				end
				if not sp_pfm_exist then
					send("alias pfm " .. expand(var["swxy_pfm"]))
					send("alias pfm_backup " .. expand(var["swxy_pfm"]))
				end
				--************************************************************************************************
				send("set wimpy 100")
				send("set wimpycmd hp " .. var["char_id"] .. "\\pfm")
				wait(0.5, function() --等待0.2s以后再fight
					exec("fight " .. var["wushi1_id"])
				end)
			end
		end
	end)
	--西夏武士神志迷糊，脚下一个不稳，倒在地上昏了过去。
	add_trigger("shouweixiangyang_5_2", "^[ > ]*(\\S+)神志迷糊，脚下一个不稳，倒在地上昏了过去。", function(params)
		if var["wushi1_name"] and var["wushi1_name"] == params[1] then --武士1昏过去
			var["wushi1_faint"] = "武士1无威胁"
			--改变pfm对象为武士2
			exec("kill " .. var["wushi1_id"])
			if var["wushi2_id"] and var["wushi2_id"] ~= "" then
				var["fight"] = 1
				var["pfm_id"] = var["wushi2_id"]
				--send("alias pfm "..expand(var["pfm1"]))
				--send("alias pfm_backup "..expand(var["pfm1"]))
				--************************************************************************************************
				local sp_pfm_exist = false
				for i = 1, 10 do
					if var["swxy_sp_skill" .. i] == nil then
						var["swxy_sp_skill" .. i] = ""
					end
					if var["swxy_sp_skill" .. i] == var["wushi2_skill"] then
						--echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."【守卫襄阳】：发现特殊技能列表【"..var["swxy_sp_skill"..i].."】与【"..var["wushi2_skill"].."】匹配！")	
						sp_pfm_exist = true
						send("alias pfm " .. expand(var["swxy_sp_pfm" .. i]))
						send("alias pfm_backup " .. expand(var["swxy_sp_pfm" .. i]))
					end
				end
				if not sp_pfm_exist then
					send("alias pfm " .. expand(var["swxy_pfm"]))
					send("alias pfm_backup " .. expand(var["swxy_pfm"]))
				end
				--************************************************************************************************
				send("set wimpy 100")
				send("set wimpycmd hp " .. var["char_id"] .. "\\pfm")
				wait(0.5, function() --等待0.2s以后再fight
					exec("fight " .. var["wushi2_id"])
				end)
			end
		end
		if var["wushi2_name"] and var["wushi2_name"] == params[1] then --武士2昏过去
			var["wushi2_faint"] = "武士2无威胁"
			--改变pfm对象为武士1
			exec("kill " .. var["wushi2_id"])
			if var["wushi1_id"] and var["wushi1_id"] ~= "" then
				var["fight"] = 1
				var["pfm_id"] = var["wushi1_id"]
				--send("alias pfm "..expand(var["pfm1"]))
				--send("alias pfm_backup "..expand(var["pfm1"]))
				--************************************************************************************************
				local sp_pfm_exist = false
				for i = 1, 10 do
					if var["swxy_sp_skill" .. i] == nil then
						var["swxy_sp_skill" .. i] = ""
					end
					if var["swxy_sp_skill" .. i] == var["wushi1_skill"] then
						--echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."【守卫襄阳】：发现特殊技能列表【"..var["swxy_sp_skill"..i].."】与【"..var["wushi1_skill"].."】匹配！")	
						sp_pfm_exist = true
						send("alias pfm " .. expand(var["swxy_sp_pfm" .. i]))
						send("alias pfm_backup " .. expand(var["swxy_sp_pfm" .. i]))
					end
				end
				if not sp_pfm_exist then
					send("alias pfm " .. expand(var["swxy_pfm"]))
					send("alias pfm_backup " .. expand(var["swxy_pfm"]))
				end
				--************************************************************************************************
				send("set wimpy 100")
				send("set wimpycmd hp " .. var["char_id"] .. "\\pfm")
				wait(0.5, function() --等待0.2s以后再fight
					exec("fight " .. var["wushi1_id"])
				end)
			end
		end
	end)
	add_trigger("shouweixiangyang_6", "^[ > ]*(看清楚一点，那并不是|你想攻击谁？)", function(params)
		del_timer("input")
		var["do_stop"] = 0
		if var["swxy_kill_count"] >= var["swxy"] * 2 then --做够指定组数，下山
			fight_end()

			exec("get gold;get gold from corpse 4;get gold from corpse 3;get gold from corpse 2;get gold from corpse 1")
			wait(4, function()
				exec("s")
				var["swxy_count"] = 0
				var["swxy_kill_count"] = 0
				var["swxy_over"] = true
			end)
		else
			wait(4, function()
				exec(
				"get gold;get gold from corpse 4;get gold from corpse 3;get gold from corpse 2;get gold from corpse 1")
				set_dazuo("swxy")
				exec("flop;yun jing;yun jingli;yun qi;check_poison check_heal do_job_swxy")
			end)
		end
	end)

	add_trigger("shouweixiangyang_7", "^[ > ]*(你速度太慢，蒙古武士已攻入玄武门，任务失败。|你擅离职守，任务失败。)", function(params)
		fight_end()
		close_fight()
		close_swxy()
		set_dazuo("changejob")
		check_busy(function()
			exec("job_fail")
		end)
	end)
	add_trigger("shouweixiangyang_8", "^[ > ]*恭喜你！你成功的完成了守卫襄阳任务！你被奖励了：", function(params) --恭喜你！你成功的完成了守卫襄阳任务！你被奖励了：
		set_end_time("守卫襄阳")
		fight_end()
		close_fight()
		close_swxy()
		set_dazuo("changejob")
		var["swxy_over"] = true
		check_busy(function()
			exec("job_win")
		end)
	end)
	add_trigger("shouweixiangyang_9",
		"^[ > ]*(城外的山谷传来一声凄厉的长啸，令人毛骨悚然。|城外厮杀的声音嘈杂无比，襄阳城墙，虽然不是雄关如铁，但是和血肉之躯比较起来，似乎也强了不少。|城下的蒙古人顿时发出了一声狂热的叫喊声，像是潮水一样冒着守兵的箭矢和滚石檑木，急切的攀爬着云梯。)",
		function(params)
			var["idle"] = 0
		end)
	--你只觉得头昏脑胀，眼前一黑，接着什么也不知道了……   失败，被打晕了
	add_trigger("shouweixiangyang_10", "^[ > ]*你只觉得头昏脑胀，眼前一黑，接着什么也不知道了……", function(params)
		echo("\n" .. C.c .. "<Lua>:" .. os.date("%m/%d %H:%M:%S") .. C.x .. "【守卫襄阳】：" .. C.R .. "很不幸，被npc打懵逼了，你眼前一黑，失败了！")
		var["time_mark_swxy"] = os.time() - 420 --失败了,提前点接守卫襄阳任务
		close_fight()
		var["no_need_heal_qi"] = nil    --可以疗伤
		var["do_stop"] = 0
		close_swxy()
	end)
	--
	--> 一阵呼啸的山风刮过，山两侧的峭壁显得格外阴森。

	--你速度太慢，西夏武士已过守卫襄阳，任务失败。

	add_alias("do_swxy", function(params)
		var["swxy_over"] = false
		var["job_zone"] = "襄阳城"
		var["job_room"] = "玄武门"
		var["log_zone"] = "襄阳城"
		var["log_room"] = "玄武门"
		var["swxy_count"] = 0
		var["swxy_kill_count"] = 0
		del_timer("wait")
		--g(1961,function()
		var["killer_name"] = "蒙古士兵"
		var["killer_id"] = "menggu wushi"
		var["pfm_id"] = "menggu wushi"
		set_wield_weapon("swxy")
		--		set_fight("swxy")
		send("alias pfm " .. expand(var["swxy_pfm"]))
		send("alias pfm_backup " .. expand(var["swxy_pfm"]))
		send("set wimpy 100")
		send("set wimpycmd hp " .. var["char_id"] .. "\\pfm")
		exec("yun_powerup none")
		--end)
	end)
	exec("do_swxy")
end

function close_swxy() --关闭触发
	del_trigger("shouweixiangyang_2")
	del_trigger("shouweixiangyang_3")
	del_trigger("shouweixiangyang_3_1")
	del_trigger("shouweixiangyang_3_2")
	del_trigger("shouweixiangyang_4")
	del_trigger("shouweixiangyang_5_1")
	del_trigger("shouweixiangyang_5_2")
	del_trigger("shouweixiangyang_6")
	del_trigger("shouweixiangyang_16")
	--del_trigger("shouweixiangyang_7") --永远保留守卫襄阳失败的触发提示
	del_trigger("shouweixiangyang_8")
	del_trigger("shouweixiangyang_9")
	del_trigger("shouweixiangyang_10")
	del_trigger("shouweixiangyang_zj_1")
	del_trigger("shouweixiangyang_zj_2")
end

function close_ask_swxy() --关闭触发
	close_trigger("shouweixiangyang_ask_1")
	close_trigger("shouweixiangyang_ask_2")
	close_trigger("shouweixiangyang_ask_3")
	close_trigger("shouweixiangyang_ask_4")
end

--check_poison check_heal
